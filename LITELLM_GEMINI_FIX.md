# LiteLLM Gemini Integration Fix

## Problem
You're encountering this error when running agent-zero:
```
litellm.exceptions.APIConnectionError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/gemini/gemini-2.0-flash',..)` Learn more: https://docs.litellm.ai/docs/providers
```

## Root Cause
The main issue is that your Google API key is incorrect. The Google API key field in your Agent Zero settings contains an OpenAI API key (`sk-proj-...`), but Google/Gemini API keys have a completely different format.

## Solution

### Step 1: Get a Valid Google AI Studio API Key

1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the API key (it should start with `AIza...`)

### Step 2: Update Your API Key Through the Web UI (Recommended)

**This is the preferred method since you're using the web UI:**

1. Open your Agent Zero web interface
2. Click the Settings icon (gear icon) in the top navigation
3. Navigate to the **"External"** tab
4. Scroll down to the **"API Keys"** section
5. Find the **"Google API Key"** field
6. Replace the current value with your new Google AI Studio API key
7. Click **"Save"** to apply the changes
8. Restart your Agent Zero application

### Alternative: Direct .env File Edit

If you prefer to edit the `.env` file directly:

```bash
# Replace this line:
API_KEY_GOOGLE=********************************************************

# With your actual Google API key:
API_KEY_GOOGLE=AIza...your-actual-google-api-key-here
```

### Step 3: Test the Fix

Run the test script to verify everything is working:

```bash
python test_litellm_gemini.py
```

This will:
- Check if your API key is properly configured
- Test the LiteLLM integration with Gemini
- Provide specific error guidance if issues remain

### Step 4: Restart Your Application

After updating the API key, restart your agent-zero application.

## Additional Improvements Made

I've also enhanced the `models.py` file with:

1. **Better Debug Logging**: Added debug logs to see exactly what model string and parameters are being passed to LiteLLM
2. **Improved Error Handling**: More specific error messages for common issues like provider recognition problems
3. **Fixed Typo**: Corrected a variable name typo in the embedding function

## Verification

After applying the fix, you should see debug logs like:
```
DEBUG:root:LiteLLMChatWrapper initialized with:
DEBUG:root:  Original model: gemini-2.0-flash
DEBUG:root:  Provider: gemini
DEBUG:root:  Formatted model: gemini/gemini-2.0-flash
DEBUG:root:  Kwargs: {'temperature': 0.0}
```

## Common Issues and Solutions

### Issue: "Authentication failed"
- **Cause**: Invalid or expired API key
- **Solution**: Generate a new API key from Google AI Studio

### Issue: "Rate limit exceeded"
- **Cause**: Too many requests to the API
- **Solution**: Wait a moment and try again, or check your API quota

### Issue: "Model not found"
- **Cause**: Model name is incorrect or not available
- **Solution**: Verify the model name in the LiteLLM documentation

## How Agent Zero API Key System Works

Understanding this will help you troubleshoot similar issues in the future:

1. **Web UI Settings**: When you enter API keys in the web UI settings, they're stored in the `api_keys` section of the settings
2. **Environment Variables**: When settings are saved, API keys are automatically written to the `.env` file with uppercase names:
   - Web UI field "Google API Key" → `.env` variable `API_KEY_GOOGLE`
   - Web UI field "OpenAI API Key" → `.env` variable `API_KEY_OPENAI`
3. **Model Loading**: When Agent Zero loads models, it looks for API keys in this order:
   - `API_KEY_{PROVIDER}` (e.g., `API_KEY_GOOGLE`)
   - `{PROVIDER}_API_KEY` (e.g., `GOOGLE_API_KEY`)
   - `{PROVIDER}_API_TOKEN` (e.g., `GOOGLE_API_TOKEN`)

## Model Configuration

Your current settings use:
- **Provider**: `GOOGLE` (mapped to `gemini` in LiteLLM)
- **Model**: `gemini-2.0-flash`
- **Final Format**: `gemini/gemini-2.0-flash`

This format is correct according to LiteLLM documentation. The issue is purely the API key authentication.

## References

- [LiteLLM Gemini Provider Documentation](https://docs.litellm.ai/docs/providers/gemini)
- [Google AI Studio API Keys](https://aistudio.google.com/app/apikey)
- [LiteLLM Supported Models](https://docs.litellm.ai/docs/providers)
